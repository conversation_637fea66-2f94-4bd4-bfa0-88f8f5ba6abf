#!/usr/bin/env python3
"""
PDF Forensic Analysis Tool
Analyzes suspicious PDF files for malicious content, flags, and exfiltration URLs.
"""

import argparse
import json
import re
import base64
import binascii
import sys
import os
from typing import List, Dict, Any, Tuple
import logging

try:
    import PyPDF2
    from PyPDF2 import PdfReader
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False

try:
    from pdfminer.high_level import extract_text
    from pdfminer.pdfparser import PDFParser
    from pdfminer.pdfdocument import PDFDocument
    from pdfminer.pdfinterp import PDFResourceManager, PDFPageInterpreter
    from pdfminer.pdfpage import PDFPage
    from pdfminer.converter import TextConverter
    from pdfminer.layout import LAParams
    from io import StringIO
    PDFMINER_AVAILABLE = True
except ImportError:
    PDFMINER_AVAILABLE = False

class PDFForensicAnalyzer:
    """Main class for PDF forensic analysis"""
    
    def __init__(self, pdf_path: str, verbose: bool = False):
        self.pdf_path = pdf_path
        self.verbose = verbose
        self.results = {
            "flags": [],
            "urls": [],
            "risky_objects": [],
            "malicious_code": [],
            "evasion_techniques": []
        }
        
        # Setup logging
        log_level = logging.DEBUG if verbose else logging.INFO
        logging.basicConfig(level=log_level, format='%(message)s')
        self.logger = logging.getLogger(__name__)
        
        # Regex patterns
        self.flag_pattern = re.compile(r'rtl\{[a-zA-Z0-9_]+\}', re.IGNORECASE)
        self.url_pattern = re.compile(r'https?://[a-zA-Z0-9\.-]+(?:/[a-zA-Z0-9\.-/]*)?', re.IGNORECASE)
        self.base64_pattern = re.compile(r'[A-Za-z0-9+/]{20,}={0,2}')
        self.hex_pattern = re.compile(r'[0-9a-fA-F]{20,}')
        
        # Risky PDF objects to look for
        self.risky_objects = [
            '/JavaScript', '/JS', '/EmbeddedFiles', '/OpenAction', '/AA',
            '/Launch', '/URI', '/SubmitForm', '/ImportData', '/GoToR'
        ]

    def log(self, message: str):
        """Log message if verbose mode is enabled"""
        if self.verbose:
            self.logger.info(message)

    def analyze(self) -> Dict[str, Any]:
        """Main analysis function"""
        self.log(f"Analyzing {self.pdf_path}...")
        
        if not os.path.exists(self.pdf_path):
            raise FileNotFoundError(f"PDF file not found: {self.pdf_path}")
        
        try:
            # Analyze with PyPDF2 if available
            if PYPDF2_AVAILABLE:
                self._analyze_with_pypdf2()
            
            # Analyze with pdfminer if available
            if PDFMINER_AVAILABLE:
                self._analyze_with_pdfminer()
            
            # Analyze raw content
            self._analyze_raw_content()
            
            # Remove duplicates
            self._deduplicate_results()
            
            return self.results
            
        except Exception as e:
            self.logger.error(f"Error analyzing PDF: {str(e)}")
            raise

    def _analyze_with_pypdf2(self):
        """Analyze PDF using PyPDF2"""
        self.log("Using PyPDF2 for analysis...")
        
        try:
            with open(self.pdf_path, 'rb') as file:
                reader = PdfReader(file)
                
                # Check if encrypted
                if reader.is_encrypted:
                    self.results["evasion_techniques"].append("PDF is encrypted")
                    self.log("PDF is encrypted - attempting to decrypt with empty password")
                    try:
                        reader.decrypt("")
                    except:
                        self.log("Failed to decrypt PDF")
                        return
                
                # Analyze metadata
                if reader.metadata:
                    self._search_in_text(str(reader.metadata), "metadata")
                
                # Analyze each page
                for page_num, page in enumerate(reader.pages):
                    self.log(f"Analyzing page {page_num + 1}")
                    
                    # Extract text content
                    try:
                        text = page.extract_text()
                        self._search_in_text(text, f"page_{page_num + 1}_text")
                    except:
                        pass
                    
                    # Analyze page objects
                    if hasattr(page, 'get_object'):
                        self._analyze_page_objects(page, page_num + 1)
                
        except Exception as e:
            self.log(f"PyPDF2 analysis failed: {str(e)}")

    def _analyze_with_pdfminer(self):
        """Analyze PDF using pdfminer"""
        self.log("Using pdfminer for analysis...")
        
        try:
            # Extract text
            text = extract_text(self.pdf_path)
            self._search_in_text(text, "pdfminer_text")
            
        except Exception as e:
            self.log(f"pdfminer analysis failed: {str(e)}")

    def _analyze_raw_content(self):
        """Analyze raw PDF content"""
        self.log("Analyzing raw PDF content...")
        
        try:
            with open(self.pdf_path, 'rb') as file:
                content = file.read()
                
            # Convert to string for analysis (ignore decode errors)
            text_content = content.decode('utf-8', errors='ignore')
            
            # Search for risky objects
            for risky_obj in self.risky_objects:
                if risky_obj.encode() in content:
                    self.results["risky_objects"].append({
                        "type": risky_obj,
                        "location": "raw_content"
                    })
                    self.log(f"Found risky object: {risky_obj}")
            
            # Search for patterns in raw content
            self._search_in_text(text_content, "raw_content")
            
            # Look for encoded content
            self._detect_encoding_techniques(text_content)
            
        except Exception as e:
            self.log(f"Raw content analysis failed: {str(e)}")

    def _analyze_page_objects(self, page, page_num: int):
        """Analyze objects within a PDF page"""
        try:
            if hasattr(page, 'get_object'):
                page_obj = page.get_object()
                self._search_in_text(str(page_obj), f"page_{page_num}_objects")
        except:
            pass

    def _search_in_text(self, text: str, source: str):
        """Search for flags and URLs in text content"""
        if not text:
            return
        
        # Search for flags
        flags = self.flag_pattern.findall(text)
        for flag in flags:
            if flag not in self.results["flags"]:
                self.results["flags"].append(flag)
                self.log(f"Found flag: {flag} in {source}")
        
        # Search for URLs
        urls = self.url_pattern.findall(text)
        for url in urls:
            if url not in self.results["urls"]:
                self.results["urls"].append(url)
                self.log(f"Found URL: {url} in {source}")
        
        # Search for JavaScript-like content
        js_patterns = [
            r'app\.launchURL\s*\(',
            r'this\.submitForm\s*\(',
            r'this\.print\s*\(',
            r'eval\s*\(',
            r'unescape\s*\('
        ]
        
        for pattern in js_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                malicious_code = f"Suspicious JavaScript: {match} in {source}"
                if malicious_code not in self.results["malicious_code"]:
                    self.results["malicious_code"].append(malicious_code)
                    self.log(f"Found suspicious code: {match}")

    def _detect_encoding_techniques(self, text: str):
        """Detect various encoding/evasion techniques"""

        # Base64 detection
        base64_matches = self.base64_pattern.findall(text)
        if base64_matches:
            self.results["evasion_techniques"].append(f"Base64 encoding detected ({len(base64_matches)} instances)")
            self.log(f"Detected {len(base64_matches)} base64 encoded strings")

            # Try to decode base64 strings
            for match in base64_matches[:5]:  # Limit to first 5 to avoid spam
                try:
                    decoded = base64.b64decode(match).decode('utf-8', errors='ignore')
                    self.log(f"Decoded base64: {decoded[:100]}...")  # Show first 100 chars
                    self._search_in_text(decoded, "base64_decoded")
                except:
                    pass

        # Hex encoding detection
        hex_matches = self.hex_pattern.findall(text)
        if hex_matches:
            self.results["evasion_techniques"].append(f"Hex encoding detected ({len(hex_matches)} instances)")
            self.log(f"Detected {len(hex_matches)} hex encoded strings")

            # Try to decode hex strings
            for match in hex_matches[:5]:  # Limit to first 5
                try:
                    decoded = binascii.unhexlify(match).decode('utf-8', errors='ignore')
                    self.log(f"Decoded hex: {decoded[:100]}...")  # Show first 100 chars
                    self._search_in_text(decoded, "hex_decoded")
                except:
                    pass

        # Additional evasion techniques
        if 'unescape(' in text.lower():
            self.results["evasion_techniques"].append("JavaScript unescape() detected")
            self.log("Detected JavaScript unescape() function")

        if 'string.fromcharcode(' in text.lower():
            self.results["evasion_techniques"].append("JavaScript String.fromCharCode() detected")
            self.log("Detected JavaScript String.fromCharCode() function")

        # Check for suspicious file extensions in embedded files
        suspicious_extensions = ['.exe', '.scr', '.bat', '.cmd', '.com', '.pif', '.vbs', '.js']
        for ext in suspicious_extensions:
            if ext in text.lower():
                self.results["evasion_techniques"].append(f"Suspicious file extension detected: {ext}")
                self.log(f"Found suspicious file extension: {ext}")

    def _deduplicate_results(self):
        """Remove duplicate entries from results"""
        self.results["flags"] = list(set(self.results["flags"]))
        self.results["urls"] = list(set(self.results["urls"]))
        self.results["malicious_code"] = list(set(self.results["malicious_code"]))
        self.results["evasion_techniques"] = list(set(self.results["evasion_techniques"]))
        
        # Deduplicate risky objects
        seen_objects = set()
        unique_objects = []
        for obj in self.results["risky_objects"]:
            obj_key = f"{obj['type']}_{obj['location']}"
            if obj_key not in seen_objects:
                seen_objects.add(obj_key)
                unique_objects.append(obj)
        self.results["risky_objects"] = unique_objects

def main():
    parser = argparse.ArgumentParser(description="PDF Forensic Analysis Tool")
    parser.add_argument("pdf_file", help="Path to the PDF file to analyze")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")
    parser.add_argument("--output", "-o", default="analysis_report.json", help="Output JSON file (default: analysis_report.json)")
    
    args = parser.parse_args()
    
    # Check dependencies
    if not PYPDF2_AVAILABLE and not PDFMINER_AVAILABLE:
        print("Error: Neither PyPDF2 nor pdfminer.six is available.")
        print("Please install dependencies: pip install PyPDF2 pdfminer.six")
        sys.exit(1)
    
    try:
        analyzer = PDFForensicAnalyzer(args.pdf_file, args.verbose)
        results = analyzer.analyze()
        
        # Save results to JSON
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"Analysis complete. Report saved to {args.output}")
        
        # Print summary
        print(f"\nSummary:")
        print(f"- Flags found: {len(results['flags'])}")
        print(f"- URLs found: {len(results['urls'])}")
        print(f"- Risky objects: {len(results['risky_objects'])}")
        print(f"- Malicious code patterns: {len(results['malicious_code'])}")
        print(f"- Evasion techniques: {len(results['evasion_techniques'])}")
        
        if results['flags']:
            print(f"\nFlags: {', '.join(results['flags'])}")
        if results['urls']:
            print(f"URLs: {', '.join(results['urls'])}")
            
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
