#!/usr/bin/env python3
"""
PDF Forensic Analysis Tool
Analyzes suspicious PDF files for malicious content, flags, and exfiltration URLs.
"""

import argparse
import json
import re
import base64
import binascii
import sys
import os
from typing import List, Dict, Any, Tuple
import logging

try:
    import PyPDF2
    from PyPDF2 import PdfReader
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False

try:
    from pdfminer.high_level import extract_text
    from pdfminer.pdfparser import PDFParser
    from pdfminer.pdfdocument import PDFDocument
    from pdfminer.pdfinterp import PDFResourceManager, PDFPageInterpreter
    from pdfminer.pdfpage import PDFPage
    from pdfminer.converter import TextConverter
    from pdfminer.layout import LAParams
    from io import StringIO
    PDFMINER_AVAILABLE = True
except ImportError:
    PDFMINER_AVAILABLE = False

class PDFForensicAnalyzer:
    """Main class for PDF forensic analysis"""
    
    def __init__(self, pdf_path: str, verbose: bool = False):
        self.pdf_path = pdf_path
        self.verbose = verbose
        self.results = {
            "embedded_malicious_code": [],
            "remote_execution_mechanisms": [],
            "embedded_flags": {
                "metadata": [],
                "streams": [],
                "objects": [],
                "annotations": [],
                "forms": [],
                "javascript": [],
                "embedded_files": []
            },
            "evasion_techniques": [],
            "exfiltration_urls": []
        }
        
        # Setup logging
        log_level = logging.DEBUG if verbose else logging.INFO
        logging.basicConfig(level=log_level, format='%(message)s')
        self.logger = logging.getLogger(__name__)
        
        # Regex patterns
        self.flag_pattern = re.compile(r'rtl\{[a-zA-Z0-9_]+\}', re.IGNORECASE)
        self.url_pattern = re.compile(r'https?://[a-zA-Z0-9\.-]+(?:/[a-zA-Z0-9\.-/]*)?', re.IGNORECASE)
        self.base64_pattern = re.compile(r'[A-Za-z0-9+/]{20,}={0,2}')
        self.hex_pattern = re.compile(r'[0-9a-fA-F]{20,}')
        
        # Risky PDF objects to look for
        self.risky_objects = [
            '/JavaScript', '/JS', '/EmbeddedFiles', '/OpenAction', '/AA',
            '/Launch', '/URI', '/SubmitForm', '/ImportData', '/GoToR'
        ]

    def log(self, message: str):
        """Log message if verbose mode is enabled"""
        if self.verbose:
            self.logger.info(message)

    def analyze(self) -> Dict[str, Any]:
        """Main analysis function"""
        self.log(f"Analyzing {self.pdf_path}...")
        
        if not os.path.exists(self.pdf_path):
            raise FileNotFoundError(f"PDF file not found: {self.pdf_path}")
        
        try:
            # Analyze with PyPDF2 if available
            if PYPDF2_AVAILABLE:
                self._analyze_with_pypdf2()
            
            # Analyze with pdfminer if available
            if PDFMINER_AVAILABLE:
                self._analyze_with_pdfminer()
            
            # Analyze raw content
            self._analyze_raw_content()
            
            # Remove duplicates
            self._deduplicate_results()
            
            return self.results
            
        except Exception as e:
            self.logger.error(f"Error analyzing PDF: {str(e)}")
            raise

    def _analyze_with_pypdf2(self):
        """Analyze PDF using PyPDF2"""
        self.log("Using PyPDF2 for analysis...")
        
        try:
            with open(self.pdf_path, 'rb') as file:
                reader = PdfReader(file)
                
                # Check if encrypted
                if reader.is_encrypted:
                    self.results["evasion_techniques"].append("PDF is encrypted")
                    self.log("PDF is encrypted - attempting to decrypt with empty password")
                    try:
                        reader.decrypt("")
                    except:
                        self.log("Failed to decrypt PDF")
                        return
                
                # Analyze metadata
                if reader.metadata:
                    self._search_in_text(str(reader.metadata), "metadata")
                
                # Analyze each page
                for page_num, page in enumerate(reader.pages):
                    self.log(f"Analyzing page {page_num + 1}")
                    
                    # Extract text content
                    try:
                        text = page.extract_text()
                        self._search_in_text(text, f"page_{page_num + 1}_text")
                    except:
                        pass
                    
                    # Analyze page objects
                    if hasattr(page, 'get_object'):
                        self._analyze_page_objects(page, page_num + 1)
                
        except Exception as e:
            self.log(f"PyPDF2 analysis failed: {str(e)}")

    def _analyze_with_pdfminer(self):
        """Analyze PDF using pdfminer"""
        self.log("Using pdfminer for analysis...")
        
        try:
            # Extract text
            text = extract_text(self.pdf_path)
            self._search_in_text(text, "pdfminer_text")
            
        except Exception as e:
            self.log(f"pdfminer analysis failed: {str(e)}")

    def _analyze_raw_content(self):
        """Analyze raw PDF content"""
        self.log("Analyzing raw PDF content...")
        
        try:
            with open(self.pdf_path, 'rb') as file:
                content = file.read()
                
            # Convert to string for analysis (ignore decode errors)
            text_content = content.decode('utf-8', errors='ignore')
            
            # Search for risky objects
            for risky_obj in self.risky_objects:
                if risky_obj.encode() in content:
                    self.results["risky_objects"].append({
                        "type": risky_obj,
                        "location": "raw_content"
                    })
                    self.log(f"Found risky object: {risky_obj}")
            
            # Search for patterns in raw content
            self._search_in_text(text_content, "raw_content")
            
            # Look for encoded content
            self._detect_encoding_techniques(text_content)
            
        except Exception as e:
            self.log(f"Raw content analysis failed: {str(e)}")

    def _analyze_page_objects(self, page, page_num: int):
        """Analyze objects within a PDF page"""
        try:
            if hasattr(page, 'get_object'):
                page_obj = page.get_object()
                self._search_in_text(str(page_obj), f"page_{page_num}_objects")
        except:
            pass

    def _search_in_text(self, text: str, source: str):
        """Search for flags, URLs, and malicious content"""
        if not text:
            return

        # Search for flags and categorize by location
        flags = self.flag_pattern.findall(text)
        for flag in flags:
            flag_entry = {"flag": flag, "location": source}

            # Categorize flags by section
            if "metadata" in source.lower():
                if flag_entry not in self.results["embedded_flags"]["metadata"]:
                    self.results["embedded_flags"]["metadata"].append(flag_entry)
            elif "stream" in source.lower() or "base64" in source.lower() or "hex" in source.lower():
                if flag_entry not in self.results["embedded_flags"]["streams"]:
                    self.results["embedded_flags"]["streams"].append(flag_entry)
            elif "object" in source.lower():
                if flag_entry not in self.results["embedded_flags"]["objects"]:
                    self.results["embedded_flags"]["objects"].append(flag_entry)
            elif "annotation" in source.lower():
                if flag_entry not in self.results["embedded_flags"]["annotations"]:
                    self.results["embedded_flags"]["annotations"].append(flag_entry)
            elif "form" in source.lower() or "xfa" in source.lower():
                if flag_entry not in self.results["embedded_flags"]["forms"]:
                    self.results["embedded_flags"]["forms"].append(flag_entry)
            elif "javascript" in source.lower() or "js" in source.lower():
                if flag_entry not in self.results["embedded_flags"]["javascript"]:
                    self.results["embedded_flags"]["javascript"].append(flag_entry)
            elif "embedded" in source.lower():
                if flag_entry not in self.results["embedded_flags"]["embedded_files"]:
                    self.results["embedded_flags"]["embedded_files"].append(flag_entry)
            else:
                # Default to objects if no specific category
                if flag_entry not in self.results["embedded_flags"]["objects"]:
                    self.results["embedded_flags"]["objects"].append(flag_entry)

            self.log(f"Found flag: {flag} in {source}")

        # Search for exfiltration URLs
        urls = self.url_pattern.findall(text)
        for url in urls:
            url_entry = {"url": url, "location": source, "type": "potential_exfiltration"}
            if url_entry not in self.results["exfiltration_urls"]:
                self.results["exfiltration_urls"].append(url_entry)
                self.log(f"Found exfiltration URL: {url} in {source}")

        # Search for embedded malicious code patterns
        malicious_patterns = [
            (r'app\.launchURL\s*\([^)]*\)', "Remote URL execution"),
            (r'this\.submitForm\s*\([^)]*\)', "Form data submission"),
            (r'this\.print\s*\([^)]*\)', "Automatic printing"),
            (r'eval\s*\([^)]*\)', "Dynamic code execution"),
            (r'unescape\s*\([^)]*\)', "Content decoding"),
            (r'String\.fromCharCode\s*\([^)]*\)', "Character encoding obfuscation"),
            (r'document\.write\s*\([^)]*\)', "Dynamic content injection"),
            (r'window\.open\s*\([^)]*\)', "Pop-up window creation"),
            (r'XMLHttpRequest\s*\([^)]*\)', "HTTP request creation"),
            (r'ActiveXObject\s*\([^)]*\)', "ActiveX object creation")
        ]

        for pattern, description in malicious_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                code_entry = {
                    "code": match,
                    "description": description,
                    "location": source,
                    "risk_level": "high" if any(x in match.lower() for x in ['eval', 'activex', 'launchurl']) else "medium"
                }
                if code_entry not in self.results["embedded_malicious_code"]:
                    self.results["embedded_malicious_code"].append(code_entry)
                    self.log(f"Found malicious code: {description} - {match[:50]}...")

        # Search for remote execution mechanisms
        remote_patterns = [
            (r'/URI\s*\([^)]*\)', "URI action for remote access"),
            (r'/Launch\s*\([^)]*\)', "External program execution"),
            (r'/GoToR\s*\([^)]*\)', "Remote document reference"),
            (r'/ImportData\s*\([^)]*\)', "External data import"),
            (r'http[s]?://[^\s<>"{}|\\^`\[\]]+', "HTTP/HTTPS URL"),
            (r'ftp://[^\s<>"{}|\\^`\[\]]+', "FTP URL")
        ]

        for pattern, description in remote_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                remote_entry = {
                    "mechanism": match,
                    "description": description,
                    "location": source,
                    "type": "remote_execution" if any(x in description.lower() for x in ['launch', 'execution']) else "data_collection"
                }
                if remote_entry not in self.results["remote_execution_mechanisms"]:
                    self.results["remote_execution_mechanisms"].append(remote_entry)
                    self.log(f"Found remote mechanism: {description} - {match[:50]}...")

    def _detect_encoding_techniques(self, text: str):
        """Detect various encoding/evasion techniques"""

        # Base64 detection
        base64_matches = self.base64_pattern.findall(text)
        if base64_matches:
            self.results["evasion_techniques"].append(f"Base64 encoding detected ({len(base64_matches)} instances)")
            self.log(f"Detected {len(base64_matches)} base64 encoded strings")

            # Try to decode base64 strings
            for match in base64_matches[:5]:  # Limit to first 5 to avoid spam
                try:
                    decoded = base64.b64decode(match).decode('utf-8', errors='ignore')
                    self.log(f"Decoded base64: {decoded[:100]}...")  # Show first 100 chars
                    self._search_in_text(decoded, "base64_decoded")
                except:
                    pass

        # Hex encoding detection
        hex_matches = self.hex_pattern.findall(text)
        if hex_matches:
            self.results["evasion_techniques"].append(f"Hex encoding detected ({len(hex_matches)} instances)")
            self.log(f"Detected {len(hex_matches)} hex encoded strings")

            # Try to decode hex strings
            for match in hex_matches[:5]:  # Limit to first 5
                try:
                    decoded = binascii.unhexlify(match).decode('utf-8', errors='ignore')
                    self.log(f"Decoded hex: {decoded[:100]}...")  # Show first 100 chars
                    self._search_in_text(decoded, "hex_decoded")
                except:
                    pass

        # Enhanced evasion technique detection
        evasion_patterns = [
            ('unescape(', "JavaScript unescape() obfuscation"),
            ('string.fromcharcode(', "JavaScript String.fromCharCode() obfuscation"),
            ('atob(', "Base64 decoding function"),
            ('btoa(', "Base64 encoding function"),
            ('escape(', "URL encoding function"),
            ('decodeuri(', "URI decoding function"),
            ('encodeuricomponent(', "URI component encoding"),
            ('\\x[0-9a-f]{2}', "Hexadecimal escape sequences"),
            ('\\u[0-9a-f]{4}', "Unicode escape sequences"),
            ('\\\\[0-9]{3}', "Octal escape sequences"),
            ('document.write', "Dynamic content injection"),
            ('innerhtml', "DOM manipulation"),
            ('createelement', "Dynamic element creation"),
            ('setattribute', "Dynamic attribute setting")
        ]

        for pattern, description in evasion_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                evasion_entry = {
                    "technique": description,
                    "pattern": pattern,
                    "location": source,
                    "risk_level": "high" if any(x in pattern for x in ['eval', 'unescape', 'fromcharcode']) else "medium"
                }
                if evasion_entry not in self.results["evasion_techniques"]:
                    self.results["evasion_techniques"].append(evasion_entry)
                    self.log(f"Detected evasion technique: {description}")

        # Check for suspicious file extensions and embedded content
        suspicious_extensions = ['.exe', '.scr', '.bat', '.cmd', '.com', '.pif', '.vbs', '.js', '.jar', '.dll', '.msi']
        for ext in suspicious_extensions:
            if ext in text.lower():
                evasion_entry = {
                    "technique": f"Suspicious file extension: {ext}",
                    "pattern": ext,
                    "location": source,
                    "risk_level": "high" if ext in ['.exe', '.scr', '.bat', '.vbs'] else "medium"
                }
                if evasion_entry not in self.results["evasion_techniques"]:
                    self.results["evasion_techniques"].append(evasion_entry)
                    self.log(f"Found suspicious file extension: {ext}")

        # Check for encryption/compression indicators
        encryption_indicators = [
            ('crypt', "Encryption-related content"),
            ('cipher', "Cipher-related content"),
            ('password', "Password-protected content"),
            ('encrypted', "Encrypted content marker"),
            ('compress', "Compressed content"),
            ('deflate', "Deflate compression"),
            ('gzip', "GZIP compression")
        ]

        for indicator, description in encryption_indicators:
            if indicator in text.lower():
                evasion_entry = {
                    "technique": description,
                    "pattern": indicator,
                    "location": source,
                    "risk_level": "medium"
                }
                if evasion_entry not in self.results["evasion_techniques"]:
                    self.results["evasion_techniques"].append(evasion_entry)
                    self.log(f"Detected encryption/compression: {description}")

    def _deduplicate_results(self):
        """Remove duplicate entries from results"""
        # Deduplicate embedded flags by section
        for section in self.results["embedded_flags"]:
            seen_flags = set()
            unique_flags = []
            for flag_entry in self.results["embedded_flags"][section]:
                flag_key = f"{flag_entry['flag']}_{flag_entry['location']}"
                if flag_key not in seen_flags:
                    seen_flags.add(flag_key)
                    unique_flags.append(flag_entry)
            self.results["embedded_flags"][section] = unique_flags

        # Deduplicate other sections
        for section in ["embedded_malicious_code", "remote_execution_mechanisms", "evasion_techniques", "exfiltration_urls"]:
            if section in self.results:
                seen_items = set()
                unique_items = []
                for item in self.results[section]:
                    if isinstance(item, dict):
                        # Create a key based on the main identifier
                        if "code" in item:
                            item_key = f"{item['code']}_{item['location']}"
                        elif "mechanism" in item:
                            item_key = f"{item['mechanism']}_{item['location']}"
                        elif "technique" in item:
                            item_key = f"{item['technique']}_{item['location']}"
                        elif "url" in item:
                            item_key = f"{item['url']}_{item['location']}"
                        else:
                            item_key = str(item)

                        if item_key not in seen_items:
                            seen_items.add(item_key)
                            unique_items.append(item)
                    else:
                        if item not in seen_items:
                            seen_items.add(item)
                            unique_items.append(item)

                self.results[section] = unique_items

def main():
    parser = argparse.ArgumentParser(description="PDF Forensic Analysis Tool")
    parser.add_argument("pdf_file", help="Path to the PDF file to analyze")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")
    parser.add_argument("--output", "-o", default="analysis_report.json", help="Output JSON file (default: analysis_report.json)")
    
    args = parser.parse_args()
    
    # Check dependencies
    if not PYPDF2_AVAILABLE and not PDFMINER_AVAILABLE:
        print("Error: Neither PyPDF2 nor pdfminer.six is available.")
        print("Please install dependencies: pip install PyPDF2 pdfminer.six")
        sys.exit(1)
    
    try:
        analyzer = PDFForensicAnalyzer(args.pdf_file, args.verbose)
        results = analyzer.analyze()
        
        # Save results to JSON
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"Analysis complete. Report saved to {args.output}")
        
        # Print enhanced summary
        print(f"\n{'='*60}")
        print("FORENSIC ANALYSIS SUMMARY")
        print(f"{'='*60}")

        # Count total flags across all sections
        total_flags = sum(len(results['embedded_flags'][section]) for section in results['embedded_flags'])

        print(f"🚩 EMBEDDED FLAGS: {total_flags}")
        for section, flags in results['embedded_flags'].items():
            if flags:
                print(f"   └─ {section.replace('_', ' ').title()}: {len(flags)}")

        print(f"🦠 EMBEDDED MALICIOUS CODE: {len(results['embedded_malicious_code'])}")
        print(f"🌐 REMOTE EXECUTION MECHANISMS: {len(results['remote_execution_mechanisms'])}")
        print(f"🎭 EVASION TECHNIQUES: {len(results['evasion_techniques'])}")
        print(f"📡 EXFILTRATION URLS: {len(results['exfiltration_urls'])}")

        # Display all flags found
        if total_flags > 0:
            print(f"\n🚩 ALL FLAGS DISCOVERED:")
            flag_count = 1
            for section, flags in results['embedded_flags'].items():
                for flag_entry in flags:
                    print(f"   {flag_count}. {flag_entry['flag']} (in {flag_entry['location']})")
                    flag_count += 1

        # Display exfiltration URLs
        if results['exfiltration_urls']:
            print(f"\n📡 EXFILTRATION URLS:")
            for i, url_entry in enumerate(results['exfiltration_urls'], 1):
                print(f"   {i}. {url_entry['url']} (in {url_entry['location']})")
            
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
