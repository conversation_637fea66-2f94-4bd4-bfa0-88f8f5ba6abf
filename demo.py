#!/usr/bin/env python3
"""
PDF Forensic Analysis Tool - Demonstration Script
Shows all features and capabilities of the tool
"""

import os
import json
import subprocess
import sys
from datetime import datetime

def print_banner():
    """Print a nice banner"""
    print("=" * 80)
    print("🔍 PDF FORENSIC ANALYSIS TOOL - DEMONSTRATION")
    print("=" * 80)
    print("Developed for malicious PDF detection and forensic analysis")
    print("Detects: Flags, URLs, Risky Objects, Malicious Code, Evasion Techniques")
    print("=" * 80)

def check_dependencies():
    """Check if all dependencies are available"""
    print("\n📋 CHECKING DEPENDENCIES...")
    
    # Check virtual environment
    if os.path.exists("venv"):
        print("✅ Virtual environment found")
    else:
        print("❌ Virtual environment not found")
        print("   Run: python3 -m venv venv")
        return False
    
    # Check analyzer script
    if os.path.exists("pdf_analyzer.py"):
        print("✅ PDF analyzer script found")
    else:
        print("❌ pdf_analyzer.py not found")
        return False
    
    # Check for PDF files
    pdf_files = [f for f in os.listdir('.') if f.lower().endswith('.pdf')]
    if pdf_files:
        print(f"✅ Found {len(pdf_files)} PDF file(s): {', '.join(pdf_files)}")
    else:
        print("❌ No PDF files found in current directory")
        return False
    
    return True

def run_demo_analysis():
    """Run the analysis demonstration"""
    print("\n🔬 RUNNING FORENSIC ANALYSIS...")
    
    # Find PDF files
    pdf_files = [f for f in os.listdir('.') if f.lower().endswith('.pdf')]
    
    for pdf_file in pdf_files:
        print(f"\n📄 Analyzing: {pdf_file}")
        print("-" * 60)
        
        # Run analysis with verbose output
        cmd = f"bash -c 'source venv/bin/activate && python pdf_analyzer.py {pdf_file} --verbose'"
        
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Analysis completed successfully")
                print("\nDetailed Output:")
                print(result.stdout)
                
                # Load and display the report
                if os.path.exists("analysis_report.json"):
                    with open("analysis_report.json", 'r') as f:
                        report = json.load(f)
                    
                    print("\n📊 DETAILED FINDINGS:")
                    print("-" * 40)
                    
                    # Display findings with emojis and formatting
                    if report.get('flags'):
                        print(f"🚩 FLAGS DISCOVERED ({len(report['flags'])}):")
                        for i, flag in enumerate(report['flags'], 1):
                            print(f"   {i}. {flag}")
                    
                    if report.get('urls'):
                        print(f"\n🌐 SUSPICIOUS URLS ({len(report['urls'])}):")
                        for i, url in enumerate(report['urls'], 1):
                            print(f"   {i}. {url}")
                    
                    if report.get('risky_objects'):
                        print(f"\n⚠️  RISKY PDF OBJECTS ({len(report['risky_objects'])}):")
                        for i, obj in enumerate(report['risky_objects'], 1):
                            print(f"   {i}. {obj['type']} (found in: {obj['location']})")
                    
                    if report.get('malicious_code'):
                        print(f"\n🦠 MALICIOUS CODE PATTERNS ({len(report['malicious_code'])}):")
                        for i, code in enumerate(report['malicious_code'], 1):
                            print(f"   {i}. {code}")
                    
                    if report.get('evasion_techniques'):
                        print(f"\n🎭 EVASION TECHNIQUES ({len(report['evasion_techniques'])}):")
                        for i, technique in enumerate(report['evasion_techniques'], 1):
                            print(f"   {i}. {technique}")
                    
                    # Save timestamped report
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    report_name = f"forensic_report_{pdf_file.replace('.pdf', '')}_{timestamp}.json"
                    with open(report_name, 'w') as f:
                        json.dump(report, f, indent=2)
                    print(f"\n💾 Detailed report saved: {report_name}")
                    
                else:
                    print("❌ No analysis report generated")
            else:
                print("❌ Analysis failed")
                print("Error:", result.stderr)
                
        except Exception as e:
            print(f"❌ Error running analysis: {str(e)}")

def show_tool_capabilities():
    """Show what the tool can detect"""
    print("\n🛠️  TOOL CAPABILITIES:")
    print("-" * 40)
    
    capabilities = [
        ("🚩 Flag Detection", "Finds flags in format rtl{...} in all PDF content"),
        ("🌐 URL Extraction", "Identifies potential exfiltration URLs"),
        ("⚠️  Risky Objects", "Detects dangerous PDF objects (/JavaScript, /EmbeddedFiles, etc.)"),
        ("🦠 Malicious Code", "Identifies suspicious JavaScript patterns"),
        ("🎭 Evasion Techniques", "Detects base64, hex encoding, and obfuscation"),
        ("🔒 Safe Analysis", "Read-only mode prevents malicious code execution"),
        ("📊 JSON Reports", "Structured output for further analysis"),
        ("🔍 Multi-Parser", "Uses PyPDF2 and pdfminer.six for comprehensive analysis")
    ]
    
    for emoji_title, description in capabilities:
        print(f"{emoji_title}: {description}")

def show_usage_examples():
    """Show usage examples"""
    print("\n📖 USAGE EXAMPLES:")
    print("-" * 40)
    
    examples = [
        ("Basic Analysis", "python pdf_analyzer.py suspicious.pdf"),
        ("Verbose Mode", "python pdf_analyzer.py suspicious.pdf --verbose"),
        ("Custom Output", "python pdf_analyzer.py suspicious.pdf --output my_report.json"),
        ("Help", "python pdf_analyzer.py --help")
    ]
    
    for title, command in examples:
        print(f"{title}:")
        print(f"   {command}")
        print()

def main():
    """Main demonstration function"""
    print_banner()
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Dependencies not met. Please install required components.")
        sys.exit(1)
    
    # Show capabilities
    show_tool_capabilities()
    
    # Show usage examples
    show_usage_examples()
    
    # Run the actual analysis
    run_demo_analysis()
    
    print("\n" + "=" * 80)
    print("🎉 DEMONSTRATION COMPLETED")
    print("=" * 80)
    print("The PDF Forensic Analysis Tool successfully identified:")
    print("• Hidden flags in various locations")
    print("• Risky PDF objects that could be malicious")
    print("• Evasion techniques used to hide content")
    print("• Suspicious code patterns")
    print("\nThis tool is ready for forensic analysis of suspicious PDF documents!")
    print("=" * 80)

if __name__ == "__main__":
    main()
