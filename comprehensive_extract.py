#!/usr/bin/env python3
"""
Comprehensive PDF Forensic Extraction Tool
Extracts findings according to specific forensic categories:
1. Embedded or obfuscated malicious code
2. Remote execution or data collection mechanisms  
3. Embedded flags in different sections
4. Evasion techniques used in the document
5. Exfiltration URLs
"""

import json
import subprocess
import sys
import os
from datetime import datetime

def run_enhanced_analysis(pdf_file):
    """Run the enhanced PDF analyzer"""
    print(f"🔍 COMPREHENSIVE FORENSIC ANALYSIS")
    print(f"📄 Target: {pdf_file}")
    print("=" * 80)
    
    if not os.path.exists(pdf_file):
        print(f"❌ Error: PDF file '{pdf_file}' not found")
        return None
    
    # Run the enhanced analyzer
    cmd = f"bash -c 'source venv/bin/activate && python pdf_analyzer.py {pdf_file} --verbose'"
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Analysis completed successfully\n")
            
            # Load the JSON report
            if os.path.exists("analysis_report.json"):
                with open("analysis_report.json", 'r') as f:
                    report = json.load(f)
                return report
            else:
                print("❌ No analysis report generated")
                return None
        else:
            print("❌ Analysis failed")
            print("Error:", result.stderr)
            return None
            
    except Exception as e:
        print(f"❌ Error running analysis: {str(e)}")
        return None

def display_embedded_malicious_code(report):
    """Display embedded or obfuscated malicious code"""
    print("🦠 EMBEDDED OR OBFUSCATED MALICIOUS CODE")
    print("-" * 60)
    
    malicious_code = report.get('embedded_malicious_code', [])
    
    if malicious_code:
        for i, code_entry in enumerate(malicious_code, 1):
            print(f"{i}. {code_entry['description']}")
            print(f"   Code: {code_entry['code']}")
            print(f"   Location: {code_entry['location']}")
            print(f"   Risk Level: {code_entry['risk_level'].upper()}")
            print()
    else:
        print("   ✅ No embedded malicious code detected")
    
    print()

def display_remote_execution_mechanisms(report):
    """Display remote execution or data collection mechanisms"""
    print("🌐 REMOTE EXECUTION OR DATA COLLECTION MECHANISMS")
    print("-" * 60)
    
    remote_mechanisms = report.get('remote_execution_mechanisms', [])
    
    if remote_mechanisms:
        for i, mechanism in enumerate(remote_mechanisms, 1):
            print(f"{i}. {mechanism['description']}")
            print(f"   Mechanism: {mechanism['mechanism']}")
            print(f"   Location: {mechanism['location']}")
            print(f"   Type: {mechanism['type'].replace('_', ' ').title()}")
            print()
    else:
        print("   ✅ No remote execution mechanisms detected")
    
    print()

def display_embedded_flags(report):
    """Display embedded flags in different sections"""
    print("🚩 EMBEDDED FLAGS IN DIFFERENT SECTIONS")
    print("-" * 60)
    
    embedded_flags = report.get('embedded_flags', {})
    total_flags = 0
    
    for section, flags in embedded_flags.items():
        if flags:
            section_name = section.replace('_', ' ').title()
            print(f"📍 {section_name} ({len(flags)} flags):")
            for flag_entry in flags:
                print(f"   • {flag_entry['flag']} (found in: {flag_entry['location']})")
                total_flags += 1
            print()
    
    if total_flags == 0:
        print("   ❌ No flags found in any section")
    else:
        print(f"📊 TOTAL FLAGS FOUND: {total_flags}")
        print("\n🎯 ALL FLAGS (Copy-Paste Ready):")
        print("-" * 30)
        for section, flags in embedded_flags.items():
            for flag_entry in flags:
                print(flag_entry['flag'])
    
    print()

def display_evasion_techniques(report):
    """Display evasion techniques used in the document"""
    print("🎭 EVASION TECHNIQUES USED IN THE DOCUMENT")
    print("-" * 60)
    
    evasion_techniques = report.get('evasion_techniques', [])
    
    if evasion_techniques:
        # Group by risk level
        high_risk = [t for t in evasion_techniques if t.get('risk_level') == 'high']
        medium_risk = [t for t in evasion_techniques if t.get('risk_level') == 'medium']
        
        if high_risk:
            print("🔴 HIGH RISK EVASION TECHNIQUES:")
            for i, technique in enumerate(high_risk, 1):
                print(f"   {i}. {technique['technique']}")
                print(f"      Pattern: {technique['pattern']}")
                print(f"      Location: {technique['location']}")
            print()
        
        if medium_risk:
            print("🟡 MEDIUM RISK EVASION TECHNIQUES:")
            for i, technique in enumerate(medium_risk, 1):
                print(f"   {i}. {technique['technique']}")
                print(f"      Pattern: {technique['pattern']}")
                print(f"      Location: {technique['location']}")
            print()
        
        # Handle legacy format (strings)
        legacy_techniques = [t for t in evasion_techniques if isinstance(t, str)]
        if legacy_techniques:
            print("📋 OTHER EVASION TECHNIQUES:")
            for i, technique in enumerate(legacy_techniques, 1):
                print(f"   {i}. {technique}")
            print()
    else:
        print("   ✅ No evasion techniques detected")
    
    print()

def display_exfiltration_urls(report):
    """Display exfiltration URLs"""
    print("📡 EXFILTRATION URLS")
    print("-" * 60)
    
    exfiltration_urls = report.get('exfiltration_urls', [])
    
    if exfiltration_urls:
        for i, url_entry in enumerate(exfiltration_urls, 1):
            print(f"{i}. {url_entry['url']}")
            print(f"   Location: {url_entry['location']}")
            print(f"   Type: {url_entry['type'].replace('_', ' ').title()}")
            print()
        
        print("🎯 ALL URLS (Copy-Paste Ready):")
        print("-" * 30)
        for url_entry in exfiltration_urls:
            print(url_entry['url'])
    else:
        print("   ✅ No exfiltration URLs detected")
    
    print()

def generate_forensic_summary(report):
    """Generate a comprehensive forensic summary"""
    print("📋 FORENSIC ANALYSIS SUMMARY")
    print("=" * 80)
    
    # Count findings
    malicious_code_count = len(report.get('embedded_malicious_code', []))
    remote_mechanisms_count = len(report.get('remote_execution_mechanisms', []))
    total_flags = sum(len(flags) for flags in report.get('embedded_flags', {}).values())
    evasion_techniques_count = len(report.get('evasion_techniques', []))
    exfiltration_urls_count = len(report.get('exfiltration_urls', []))
    
    print(f"🦠 Embedded Malicious Code: {malicious_code_count}")
    print(f"🌐 Remote Execution Mechanisms: {remote_mechanisms_count}")
    print(f"🚩 Embedded Flags: {total_flags}")
    print(f"🎭 Evasion Techniques: {evasion_techniques_count}")
    print(f"📡 Exfiltration URLs: {exfiltration_urls_count}")
    
    # Risk assessment
    total_threats = malicious_code_count + remote_mechanisms_count + evasion_techniques_count + exfiltration_urls_count
    
    if total_threats == 0:
        risk_level = "LOW"
        risk_color = "🟢"
    elif total_threats <= 3:
        risk_level = "MEDIUM"
        risk_color = "🟡"
    else:
        risk_level = "HIGH"
        risk_color = "🔴"
    
    print(f"\n{risk_color} OVERALL RISK LEVEL: {risk_level}")
    print(f"📊 Total Security Indicators: {total_threats}")
    
    if total_flags > 0:
        print(f"🎯 CTF/Challenge Flags Found: {total_flags}")

def main():
    if len(sys.argv) != 2:
        print("Usage: python comprehensive_extract.py <pdf_file>")
        print("\nThis tool performs comprehensive forensic analysis and extracts:")
        print("1. Embedded or obfuscated malicious code")
        print("2. Remote execution or data collection mechanisms")
        print("3. Embedded flags in different sections")
        print("4. Evasion techniques used in the document")
        print("5. Exfiltration URLs")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    
    # Run analysis
    report = run_enhanced_analysis(pdf_file)
    
    if not report:
        print("❌ Analysis failed. Cannot generate report.")
        sys.exit(1)
    
    # Display findings by category
    display_embedded_malicious_code(report)
    display_remote_execution_mechanisms(report)
    display_embedded_flags(report)
    display_evasion_techniques(report)
    display_exfiltration_urls(report)
    
    # Generate summary
    generate_forensic_summary(report)
    
    # Save detailed report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    detailed_report_name = f"comprehensive_forensic_report_{timestamp}.json"
    with open(detailed_report_name, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n💾 Detailed forensic report saved: {detailed_report_name}")
    print("=" * 80)

if __name__ == "__main__":
    main()
