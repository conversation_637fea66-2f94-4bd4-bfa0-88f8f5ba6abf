#!/usr/bin/env python3
"""
Deep PDF Forensic Analysis Tool
Performs comprehensive analysis to find all hidden flags and URLs
"""

import re
import base64
import binascii
import json
import sys
import os
from typing import List, Dict, Set

try:
    import PyPDF2
    from PyPDF2 import PdfReader
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False

try:
    from pdfminer.high_level import extract_text
    from pdfminer.pdfparser import PDFParser
    from pdfminer.pdfdocument import PDFDocument
    from pdfminer.pdfinterp import PDFResourceManager, PDFPageInterpreter
    from pdfminer.pdfpage import PDFPage
    from pdfminer.converter import TextConverter
    from pdfminer.layout import LAParams
    from io import StringIO
    PDFMINER_AVAILABLE = True
except ImportError:
    PDFMINER_AVAILABLE = False

class DeepPDFAnalyzer:
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.flags = set()
        self.urls = set()
        self.all_content = []
        
        # Enhanced patterns
        self.flag_pattern = re.compile(r'rtl\{[^}]+\}', re.IGNORECASE)
        self.url_pattern = re.compile(r'https?://[a-zA-Z0-9\.-]+(?:/[^\s<>"\']*)?', re.IGNORECASE)
        self.base64_pattern = re.compile(r'[A-Za-z0-9+/]{20,}={0,2}')
        self.hex_pattern = re.compile(r'[0-9a-fA-F]{20,}')
        
    def log(self, message: str):
        print(f"[DEBUG] {message}")
    
    def extract_from_text(self, text: str, source: str):
        """Extract flags and URLs from text"""
        if not text:
            return
            
        self.all_content.append(f"=== {source} ===\n{text}\n")
        
        # Find flags
        flags = self.flag_pattern.findall(text)
        for flag in flags:
            self.flags.add(flag)
            self.log(f"Found flag: {flag} in {source}")
        
        # Find URLs
        urls = self.url_pattern.findall(text)
        for url in urls:
            self.urls.add(url)
            self.log(f"Found URL: {url} in {source}")
    
    def decode_and_search(self, encoded_text: str, encoding_type: str, source: str):
        """Decode text and search for flags/URLs"""
        try:
            if encoding_type == "base64":
                decoded = base64.b64decode(encoded_text).decode('utf-8', errors='ignore')
                self.log(f"Decoded base64: {decoded[:100]}...")
            elif encoding_type == "hex":
                decoded = binascii.unhexlify(encoded_text).decode('utf-8', errors='ignore')
                self.log(f"Decoded hex: {decoded[:100]}...")
            else:
                return
                
            self.extract_from_text(decoded, f"{source}_decoded_{encoding_type}")
        except Exception as e:
            self.log(f"Failed to decode {encoding_type}: {str(e)}")
    
    def analyze_with_pypdf2(self):
        """Deep analysis with PyPDF2"""
        self.log("Starting PyPDF2 deep analysis...")
        
        try:
            with open(self.pdf_path, 'rb') as file:
                reader = PdfReader(file)
                
                # Check encryption
                if reader.is_encrypted:
                    self.log("PDF is encrypted, attempting to decrypt...")
                    try:
                        reader.decrypt("")
                    except:
                        self.log("Failed to decrypt PDF")
                        return
                
                # Extract metadata
                if reader.metadata:
                    metadata_str = str(reader.metadata)
                    self.extract_from_text(metadata_str, "metadata")
                
                # Analyze each page
                for page_num, page in enumerate(reader.pages):
                    self.log(f"Analyzing page {page_num + 1}")
                    
                    # Extract text
                    try:
                        text = page.extract_text()
                        self.extract_from_text(text, f"page_{page_num + 1}_text")
                    except Exception as e:
                        self.log(f"Failed to extract text from page {page_num + 1}: {e}")
                    
                    # Analyze page object
                    try:
                        page_obj = page.get_object()
                        page_obj_str = str(page_obj)
                        self.extract_from_text(page_obj_str, f"page_{page_num + 1}_object")
                    except Exception as e:
                        self.log(f"Failed to get page object {page_num + 1}: {e}")
                
                # Try to access the PDF's internal structure
                try:
                    # Get the document root
                    if hasattr(reader, 'trailer') and reader.trailer:
                        trailer_str = str(reader.trailer)
                        self.extract_from_text(trailer_str, "trailer")
                    
                    # Try to get catalog
                    if hasattr(reader, 'trailer') and '/Root' in reader.trailer:
                        root_ref = reader.trailer['/Root']
                        if hasattr(root_ref, 'get_object'):
                            root_obj = root_ref.get_object()
                            root_str = str(root_obj)
                            self.extract_from_text(root_str, "catalog")
                except Exception as e:
                    self.log(f"Failed to analyze PDF structure: {e}")
                    
        except Exception as e:
            self.log(f"PyPDF2 analysis failed: {str(e)}")
    
    def analyze_with_pdfminer(self):
        """Deep analysis with pdfminer"""
        self.log("Starting pdfminer deep analysis...")
        
        try:
            # Extract all text
            text = extract_text(self.pdf_path)
            self.extract_from_text(text, "pdfminer_full_text")
            
            # Parse PDF structure
            with open(self.pdf_path, 'rb') as file:
                parser = PDFParser(file)
                document = PDFDocument(parser)
                
                # Extract document info
                if document.info:
                    for info in document.info:
                        info_str = str(info)
                        self.extract_from_text(info_str, "document_info")
                
                # Try to get catalog
                if hasattr(document, 'catalog'):
                    catalog_str = str(document.catalog)
                    self.extract_from_text(catalog_str, "pdfminer_catalog")
                    
        except Exception as e:
            self.log(f"pdfminer analysis failed: {str(e)}")
    
    def analyze_raw_content(self):
        """Analyze raw PDF content"""
        self.log("Starting raw content analysis...")
        
        try:
            with open(self.pdf_path, 'rb') as file:
                raw_content = file.read()
            
            # Convert to text (ignore decode errors)
            text_content = raw_content.decode('utf-8', errors='ignore')
            self.extract_from_text(text_content, "raw_content")
            
            # Look for base64 encoded content
            base64_matches = self.base64_pattern.findall(text_content)
            self.log(f"Found {len(base64_matches)} potential base64 strings")
            
            for i, match in enumerate(base64_matches[:10]):  # Limit to first 10
                self.decode_and_search(match, "base64", f"raw_base64_{i}")
            
            # Look for hex encoded content
            hex_matches = self.hex_pattern.findall(text_content)
            self.log(f"Found {len(hex_matches)} potential hex strings")
            
            for i, match in enumerate(hex_matches[:10]):  # Limit to first 10
                if len(match) % 2 == 0:  # Valid hex length
                    self.decode_and_search(match, "hex", f"raw_hex_{i}")
            
            # Search for specific PDF objects and streams
            pdf_objects = [
                b'/JavaScript', b'/JS', b'/EmbeddedFiles', b'/OpenAction', 
                b'/AA', b'/URI', b'/Launch', b'/SubmitForm', b'/ImportData',
                b'/XFA', b'/RichMedia', b'/Metadata'
            ]
            
            for obj in pdf_objects:
                if obj in raw_content:
                    self.log(f"Found PDF object: {obj.decode()}")
                    # Extract surrounding content
                    start_pos = raw_content.find(obj)
                    if start_pos != -1:
                        # Get 500 bytes before and after
                        context_start = max(0, start_pos - 500)
                        context_end = min(len(raw_content), start_pos + 500)
                        context = raw_content[context_start:context_end]
                        context_text = context.decode('utf-8', errors='ignore')
                        self.extract_from_text(context_text, f"object_context_{obj.decode()[1:]}")
            
        except Exception as e:
            self.log(f"Raw content analysis failed: {str(e)}")
    
    def search_for_hidden_content(self):
        """Search for hidden content using various techniques"""
        self.log("Searching for hidden content...")
        
        # Search in all collected content for additional patterns
        all_text = "\n".join(self.all_content)
        
        # Look for URL-encoded content
        url_encoded_pattern = re.compile(r'%[0-9a-fA-F]{2}')
        if url_encoded_pattern.search(all_text):
            self.log("Found URL-encoded content")
            # Try to decode URL-encoded strings
            import urllib.parse
            try:
                decoded_url = urllib.parse.unquote(all_text)
                self.extract_from_text(decoded_url, "url_decoded")
            except:
                pass
        
        # Look for JavaScript string concatenation
        js_concat_pattern = re.compile(r'["\'][^"\']*["\'][\s]*\+[\s]*["\'][^"\']*["\']')
        js_matches = js_concat_pattern.findall(all_text)
        for match in js_matches:
            self.log(f"Found JS concatenation: {match}")
            # Try to evaluate simple concatenations safely
            try:
                # Remove quotes and plus signs, concatenate
                cleaned = re.sub(r'["\'][\s]*\+[\s]*["\']', '', match)
                cleaned = cleaned.strip('"\'')
                self.extract_from_text(cleaned, "js_concatenated")
            except:
                pass
    
    def analyze(self):
        """Perform complete analysis"""
        print(f"🔍 DEEP FORENSIC ANALYSIS OF: {self.pdf_path}")
        print("=" * 80)
        
        if not os.path.exists(self.pdf_path):
            print(f"❌ Error: PDF file not found: {self.pdf_path}")
            return
        
        # Run all analysis methods
        if PYPDF2_AVAILABLE:
            self.analyze_with_pypdf2()
        
        if PDFMINER_AVAILABLE:
            self.analyze_with_pdfminer()
        
        self.analyze_raw_content()
        self.search_for_hidden_content()
        
        # Final search in all collected content
        all_content_text = "\n".join(self.all_content)
        self.extract_from_text(all_content_text, "final_comprehensive_search")
        
        # Display results
        self.display_results()
    
    def display_results(self):
        """Display all findings"""
        print("\n" + "=" * 80)
        print("🎯 FORENSIC ANALYSIS RESULTS")
        print("=" * 80)
        
        print(f"🚩 FLAGS FOUND ({len(self.flags)}):")
        if self.flags:
            for i, flag in enumerate(sorted(self.flags), 1):
                print(f"   {i}. {flag}")
        else:
            print("   ❌ No flags found")
        
        print(f"\n📡 URLS FOUND ({len(self.urls)}):")
        if self.urls:
            for i, url in enumerate(sorted(self.urls), 1):
                print(f"   {i}. {url}")
        else:
            print("   ❌ No URLs found")
        
        print(f"\n📋 SUMMARY:")
        print(f"   • Total Flags: {len(self.flags)}")
        print(f"   • Total URLs: {len(self.urls)}")
        
        # Save results
        results = {
            "flags": list(self.flags),
            "urls": list(self.urls),
            "analysis_timestamp": "2025-08-05",
            "pdf_file": self.pdf_path
        }
        
        with open("deep_analysis_results.json", "w") as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 Results saved to: deep_analysis_results.json")

def main():
    if len(sys.argv) != 2:
        print("Usage: python deep_forensic_analyzer.py <pdf_file>")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    analyzer = DeepPDFAnalyzer(pdf_file)
    analyzer.analyze()

if __name__ == "__main__":
    main()
