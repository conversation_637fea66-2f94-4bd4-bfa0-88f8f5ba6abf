#!/usr/bin/env python3
"""
Test script for PDF Forensic Analysis Tool
Demonstrates the tool's capabilities and validates functionality
"""

import os
import json
import subprocess
import sys

def run_analysis(pdf_file, verbose=True):
    """Run the PDF analyzer on a given file"""
    print(f"\n{'='*60}")
    print(f"ANALYZING: {pdf_file}")
    print(f"{'='*60}")
    
    if not os.path.exists(pdf_file):
        print(f"❌ Error: PDF file '{pdf_file}' not found")
        return None
    
    # Run the analyzer
    cmd = ["python", "pdf_analyzer.py", pdf_file]
    if verbose:
        cmd.append("--verbose")
    
    try:
        # Use bash explicitly and activate virtual environment
        bash_cmd = f"bash -c 'source venv/bin/activate && {' '.join(cmd)}'"
        result = subprocess.run(
            bash_cmd,
            shell=True,
            capture_output=True,
            text=True,
            cwd=os.getcwd()
        )
        
        if result.returncode == 0:
            print("✅ Analysis completed successfully")
            print("\nOutput:")
            print(result.stdout)
            
            # Load and display the JSON report
            if os.path.exists("analysis_report.json"):
                with open("analysis_report.json", 'r') as f:
                    report = json.load(f)
                return report
            else:
                print("❌ No report file generated")
                return None
        else:
            print("❌ Analysis failed")
            print("Error output:")
            print(result.stderr)
            return None
            
    except Exception as e:
        print(f"❌ Error running analysis: {str(e)}")
        return None

def display_report_summary(report):
    """Display a formatted summary of the analysis report"""
    if not report:
        return
    
    print(f"\n{'='*60}")
    print("ANALYSIS SUMMARY")
    print(f"{'='*60}")
    
    # Flags
    if report.get('flags'):
        print(f"🚩 FLAGS FOUND ({len(report['flags'])}):")
        for flag in report['flags']:
            print(f"   • {flag}")
    else:
        print("🚩 FLAGS: None found")
    
    # URLs
    if report.get('urls'):
        print(f"\n🌐 URLS FOUND ({len(report['urls'])}):")
        for url in report['urls']:
            print(f"   • {url}")
    else:
        print("\n🌐 URLS: None found")
    
    # Risky Objects
    if report.get('risky_objects'):
        print(f"\n⚠️  RISKY OBJECTS ({len(report['risky_objects'])}):")
        for obj in report['risky_objects']:
            print(f"   • {obj['type']} (in {obj['location']})")
    else:
        print("\n⚠️  RISKY OBJECTS: None found")
    
    # Malicious Code
    if report.get('malicious_code'):
        print(f"\n🦠 MALICIOUS CODE PATTERNS ({len(report['malicious_code'])}):")
        for code in report['malicious_code']:
            print(f"   • {code}")
    else:
        print("\n🦠 MALICIOUS CODE: None found")
    
    # Evasion Techniques
    if report.get('evasion_techniques'):
        print(f"\n🎭 EVASION TECHNIQUES ({len(report['evasion_techniques'])}):")
        for technique in report['evasion_techniques']:
            print(f"   • {technique}")
    else:
        print("\n🎭 EVASION TECHNIQUES: None found")

def main():
    """Main test function"""
    print("PDF Forensic Analysis Tool - Test Suite")
    print("=" * 60)
    
    # Check if virtual environment exists
    if not os.path.exists("venv"):
        print("❌ Virtual environment not found. Please run:")
        print("   python3 -m venv venv")
        print("   source venv/bin/activate")
        print("   pip install PyPDF2 pdfminer.six")
        sys.exit(1)
    
    # Check if analyzer exists
    if not os.path.exists("pdf_analyzer.py"):
        print("❌ pdf_analyzer.py not found in current directory")
        sys.exit(1)
    
    # Find PDF files in current directory
    pdf_files = [f for f in os.listdir('.') if f.lower().endswith('.pdf')]
    
    if not pdf_files:
        print("❌ No PDF files found in current directory")
        sys.exit(1)
    
    print(f"Found {len(pdf_files)} PDF file(s):")
    for pdf in pdf_files:
        print(f"   • {pdf}")
    
    # Analyze each PDF file
    for pdf_file in pdf_files:
        report = run_analysis(pdf_file, verbose=True)
        display_report_summary(report)
        
        # Save individual report with PDF name
        if report:
            report_name = f"report_{pdf_file.replace('.pdf', '')}.json"
            with open(report_name, 'w') as f:
                json.dump(report, f, indent=2)
            print(f"\n📄 Detailed report saved to: {report_name}")
    
    print(f"\n{'='*60}")
    print("TEST SUITE COMPLETED")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
