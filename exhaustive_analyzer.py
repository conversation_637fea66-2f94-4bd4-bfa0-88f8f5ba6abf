#!/usr/bin/env python3
"""
Exhaustive PDF Forensic Analysis Tool
Searches for ALL hidden flags and URLs using every possible technique
"""

import re
import base64
import binascii
import json
import sys
import os
import zlib
import urllib.parse
from typing import List, Dict, Set

try:
    import PyPDF2
    from PyPDF2 import PdfReader
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False

class ExhaustivePDFAnalyzer:
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.flags = set()
        self.urls = set()
        self.all_content = []
        
        # Enhanced patterns
        self.flag_pattern = re.compile(r'rtl\{[^}]+\}', re.IGNORECASE)
        self.url_pattern = re.compile(r'https?://[a-zA-Z0-9\.-]+(?:/[^\s<>"\']*)?', re.IGNORECASE)
        
        # Multiple encoding patterns
        self.base64_patterns = [
            re.compile(r'[A-Za-z0-9+/]{20,}={0,2}'),
            re.compile(r'[A-Za-z0-9+/]{16,}={0,2}'),
            re.compile(r'[A-Za-z0-9+/]{12,}={0,2}')
        ]
        self.hex_patterns = [
            re.compile(r'[0-9a-fA-F]{20,}'),
            re.compile(r'[0-9a-fA-F]{16,}'),
            re.compile(r'[0-9a-fA-F]{12,}')
        ]
        
    def log(self, message: str):
        print(f"[SEARCH] {message}")
    
    def extract_from_text(self, text: str, source: str):
        """Extract flags and URLs from text"""
        if not text:
            return
            
        # Find flags
        flags = self.flag_pattern.findall(text)
        for flag in flags:
            if flag not in self.flags:
                self.flags.add(flag)
                self.log(f"🚩 NEW FLAG: {flag} in {source}")
        
        # Find URLs
        urls = self.url_pattern.findall(text)
        for url in urls:
            if url not in self.urls:
                self.urls.add(url)
                self.log(f"📡 NEW URL: {url} in {source}")
    
    def try_all_decodings(self, encoded_text: str, source: str):
        """Try multiple decoding methods"""
        decodings_tried = []
        
        # Base64 decoding
        try:
            decoded = base64.b64decode(encoded_text).decode('utf-8', errors='ignore')
            if decoded and len(decoded) > 3:
                decodings_tried.append(f"base64: {decoded[:100]}...")
                self.extract_from_text(decoded, f"{source}_base64_decoded")
        except:
            pass
        
        # URL decoding
        try:
            decoded = urllib.parse.unquote(encoded_text)
            if decoded != encoded_text:
                decodings_tried.append(f"url: {decoded[:100]}...")
                self.extract_from_text(decoded, f"{source}_url_decoded")
        except:
            pass
        
        # Hex decoding
        try:
            if len(encoded_text) % 2 == 0:
                decoded = binascii.unhexlify(encoded_text).decode('utf-8', errors='ignore')
                if decoded and len(decoded) > 3:
                    decodings_tried.append(f"hex: {decoded[:100]}...")
                    self.extract_from_text(decoded, f"{source}_hex_decoded")
        except:
            pass
        
        # ROT13
        try:
            import codecs
            decoded = codecs.decode(encoded_text, 'rot13')
            if decoded != encoded_text:
                decodings_tried.append(f"rot13: {decoded[:100]}...")
                self.extract_from_text(decoded, f"{source}_rot13_decoded")
        except:
            pass
        
        # Try as ASCII values
        try:
            if all(c.isdigit() or c.isspace() for c in encoded_text):
                numbers = encoded_text.split()
                if len(numbers) > 3:
                    ascii_chars = ''.join(chr(int(n)) for n in numbers if n.isdigit() and 0 <= int(n) <= 127)
                    if ascii_chars:
                        decodings_tried.append(f"ascii: {ascii_chars[:100]}...")
                        self.extract_from_text(ascii_chars, f"{source}_ascii_decoded")
        except:
            pass
        
        if decodings_tried:
            self.log(f"Decoded {len(decodings_tried)} variants from {source}")
    
    def analyze_pdf_objects(self):
        """Analyze PDF objects in detail"""
        self.log("Analyzing PDF objects in detail...")
        
        try:
            with open(self.pdf_path, 'rb') as file:
                content = file.read()
            
            # Find all object definitions
            obj_pattern = re.compile(rb'(\d+)\s+(\d+)\s+obj\s*(.*?)\s*endobj', re.DOTALL)
            objects = obj_pattern.findall(content)
            
            self.log(f"Found {len(objects)} PDF objects")
            
            for obj_num, gen_num, obj_content in objects:
                obj_text = obj_content.decode('utf-8', errors='ignore')
                self.extract_from_text(obj_text, f"object_{obj_num.decode()}_{gen_num.decode()}")
                
                # Look for streams within objects
                if b'stream' in obj_content and b'endstream' in obj_content:
                    stream_pattern = re.compile(rb'stream\s*(.*?)\s*endstream', re.DOTALL)
                    streams = stream_pattern.findall(obj_content)
                    
                    for i, stream_data in enumerate(streams):
                        stream_text = stream_data.decode('utf-8', errors='ignore')
                        self.extract_from_text(stream_text, f"stream_{obj_num.decode()}_{i}")
                        
                        # Try to decompress if it looks compressed
                        try:
                            decompressed = zlib.decompress(stream_data)
                            decompressed_text = decompressed.decode('utf-8', errors='ignore')
                            self.extract_from_text(decompressed_text, f"decompressed_stream_{obj_num.decode()}_{i}")
                        except:
                            pass
                        
                        # Try various decodings on stream data
                        self.try_all_decodings(stream_text, f"stream_{obj_num.decode()}_{i}")
        
        except Exception as e:
            self.log(f"Object analysis failed: {e}")
    
    def analyze_javascript_content(self):
        """Look for JavaScript content and decode it"""
        self.log("Searching for JavaScript content...")
        
        try:
            with open(self.pdf_path, 'rb') as file:
                content = file.read()
            
            # Look for JavaScript keywords and surrounding content
            js_keywords = [b'JavaScript', b'/JS', b'eval', b'unescape', b'String.fromCharCode']
            
            for keyword in js_keywords:
                pos = 0
                while True:
                    pos = content.find(keyword, pos)
                    if pos == -1:
                        break
                    
                    # Extract context around JavaScript
                    start = max(0, pos - 1000)
                    end = min(len(content), pos + 1000)
                    context = content[start:end]
                    context_text = context.decode('utf-8', errors='ignore')
                    
                    self.extract_from_text(context_text, f"js_context_{keyword.decode()}")
                    self.try_all_decodings(context_text, f"js_context_{keyword.decode()}")
                    
                    pos += len(keyword)
        
        except Exception as e:
            self.log(f"JavaScript analysis failed: {e}")
    
    def analyze_form_data(self):
        """Analyze form data and XFA content"""
        self.log("Analyzing form data and XFA content...")
        
        try:
            with open(self.pdf_path, 'rb') as file:
                content = file.read()
            
            # Look for form-related keywords
            form_keywords = [b'XFA', b'AcroForm', b'Field', b'Widget', b'Annot']
            
            for keyword in form_keywords:
                pos = 0
                while True:
                    pos = content.find(keyword, pos)
                    if pos == -1:
                        break
                    
                    # Extract context
                    start = max(0, pos - 500)
                    end = min(len(content), pos + 500)
                    context = content[start:end]
                    context_text = context.decode('utf-8', errors='ignore')
                    
                    self.extract_from_text(context_text, f"form_context_{keyword.decode()}")
                    self.try_all_decodings(context_text, f"form_context_{keyword.decode()}")
                    
                    pos += len(keyword)
        
        except Exception as e:
            self.log(f"Form analysis failed: {e}")
    
    def analyze_metadata_and_comments(self):
        """Analyze metadata and comments"""
        self.log("Analyzing metadata and comments...")
        
        try:
            with open(self.pdf_path, 'rb') as file:
                content = file.read()
            
            # Look for comment patterns
            comment_patterns = [
                rb'%[^\r\n]*',  # PDF comments
                rb'<!--.*?-->',  # HTML comments
                rb'//[^\r\n]*',  # JavaScript comments
                rb'/\*.*?\*/'   # Multi-line comments
            ]
            
            for pattern in comment_patterns:
                matches = re.findall(pattern, content, re.DOTALL)
                for i, match in enumerate(matches):
                    comment_text = match.decode('utf-8', errors='ignore')
                    self.extract_from_text(comment_text, f"comment_{i}")
                    self.try_all_decodings(comment_text, f"comment_{i}")
        
        except Exception as e:
            self.log(f"Metadata analysis failed: {e}")
    
    def search_with_different_patterns(self):
        """Search with different flag and URL patterns"""
        self.log("Searching with alternative patterns...")
        
        try:
            with open(self.pdf_path, 'rb') as file:
                content = file.read()
            
            text_content = content.decode('utf-8', errors='ignore')
            
            # Alternative flag patterns
            alt_flag_patterns = [
                re.compile(r'rtl\{[^}]*\}', re.IGNORECASE),
                re.compile(r'rtl\{.*?\}', re.IGNORECASE | re.DOTALL),
                re.compile(r'rtl\s*\{\s*[^}]*\s*\}', re.IGNORECASE),
                re.compile(r'[rR][tT][lL]\{[^}]+\}'),
            ]
            
            for i, pattern in enumerate(alt_flag_patterns):
                matches = pattern.findall(text_content)
                for match in matches:
                    if match not in self.flags:
                        self.flags.add(match)
                        self.log(f"🚩 ALT FLAG: {match} (pattern {i})")
            
            # Alternative URL patterns
            alt_url_patterns = [
                re.compile(r'https?://[^\s<>"\']+', re.IGNORECASE),
                re.compile(r'http[s]?://[a-zA-Z0-9\.-]+[^\s<>"\']*', re.IGNORECASE),
                re.compile(r'https?://[a-zA-Z0-9\.-]+(?:/[^\s]*)?', re.IGNORECASE),
            ]
            
            for i, pattern in enumerate(alt_url_patterns):
                matches = pattern.findall(text_content)
                for match in matches:
                    if match not in self.urls:
                        self.urls.add(match)
                        self.log(f"📡 ALT URL: {match} (pattern {i})")
        
        except Exception as e:
            self.log(f"Alternative pattern search failed: {e}")
    
    def analyze(self):
        """Perform exhaustive analysis"""
        print(f"🔍 EXHAUSTIVE FORENSIC ANALYSIS OF: {self.pdf_path}")
        print("=" * 80)
        
        if not os.path.exists(self.pdf_path):
            print(f"❌ Error: PDF file not found: {self.pdf_path}")
            return
        
        # Run all analysis methods
        self.analyze_pdf_objects()
        self.analyze_javascript_content()
        self.analyze_form_data()
        self.analyze_metadata_and_comments()
        self.search_with_different_patterns()
        
        # Display results
        self.display_results()
    
    def display_results(self):
        """Display all findings"""
        print("\n" + "=" * 80)
        print("🎯 EXHAUSTIVE ANALYSIS RESULTS")
        print("=" * 80)
        
        print(f"🚩 FLAGS FOUND ({len(self.flags)}):")
        if self.flags:
            for i, flag in enumerate(sorted(self.flags), 1):
                print(f"   {i}. {flag}")
        else:
            print("   ❌ No flags found")
        
        print(f"\n📡 URLS FOUND ({len(self.urls)}):")
        if self.urls:
            for i, url in enumerate(sorted(self.urls), 1):
                print(f"   {i}. {url}")
        else:
            print("   ❌ No URLs found")
        
        print(f"\n📋 CHALLENGE ANSWERS:")
        print("=" * 40)
        
        all_findings = list(self.flags) + list(self.urls)
        all_findings.sort()
        
        for i in range(5):  # Show 5 entries as requested
            if i < len(all_findings):
                print(f"Flag/URL {i+1}: {all_findings[i]}")
            else:
                print(f"Flag/URL {i+1}: [NOT FOUND]")
        
        # Save results
        results = {
            "flags": list(self.flags),
            "urls": list(self.urls),
            "total_findings": len(self.flags) + len(self.urls),
            "analysis_timestamp": "2025-08-05",
            "pdf_file": self.pdf_path
        }
        
        with open("exhaustive_analysis_results.json", "w") as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 Results saved to: exhaustive_analysis_results.json")

def main():
    if len(sys.argv) != 2:
        print("Usage: python exhaustive_analyzer.py <pdf_file>")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    analyzer = ExhaustivePDFAnalyzer(pdf_file)
    analyzer.analyze()

if __name__ == "__main__":
    main()
