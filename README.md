# PDF Forensic Analysis Tool

A comprehensive Python-based command-line tool for forensic analysis of suspicious PDF documents. This tool identifies embedded malicious code, flags, exfiltration URLs, and evasion techniques commonly used in malicious PDFs.

## Features

- **Flag Detection**: Searches for flags in the format `rtl{...}` in metadata, comments, annotations, and streams
- **URL Extraction**: Identifies potential exfiltration URLs (`https://website` or `https://website/endpoint`)
- **Risky Object Detection**: Scans for dangerous PDF objects like `/JavaScript`, `/EmbeddedFiles`, `/OpenAction`, etc.
- **Malicious Code Analysis**: Detects suspicious JavaScript patterns and embedded payloads
- **Evasion Technique Detection**: Identifies base64 encoding, hex encoding, and other obfuscation methods
- **Safe Analysis**: Runs in read-only mode to prevent accidental execution of malicious code
- **Multiple Parser Support**: Uses both PyPDF2 and pdfminer.six for comprehensive analysis
- **JSON Output**: Generates structured reports for further analysis

## Installation

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Install Dependencies

```bash
# Install required packages
pip install PyPDF2 pdfminer.six

# Or install from requirements.txt (if provided)
pip install -r requirements.txt
```

### Alternative Installation Methods

```bash
# Using conda
conda install -c conda-forge pypdf2 pdfminer.six

# Using pipenv
pipenv install PyPDF2 pdfminer.six
```

## Usage

### Basic Usage

```bash
python pdf_analyzer.py suspicious.pdf
```

### Verbose Mode

```bash
python pdf_analyzer.py suspicious.pdf --verbose
```

### Custom Output File

```bash
python pdf_analyzer.py suspicious.pdf --output my_report.json
```

### Full Command Options

```bash
python pdf_analyzer.py [PDF_FILE] [OPTIONS]

Arguments:
  PDF_FILE              Path to the PDF file to analyze

Options:
  -h, --help           Show help message and exit
  -v, --verbose        Enable verbose output with detailed analysis steps
  -o, --output OUTPUT  Specify output JSON file (default: analysis_report.json)
```

## Example Output

### Console Output (with --verbose)
```
Analyzing CMAB-PDF.pdf...
Using PyPDF2 for analysis...
Analyzing page 1
Using pdfminer for analysis...
[... detailed pdfminer parsing output ...]
Analyzing raw PDF content...
Found risky object: /JavaScript
Found risky object: /EmbeddedFiles
Found risky object: /OpenAction
Found risky object: /AA
Found risky object: /SubmitForm
Found flag: rtl{r1chmedia_backd00r} in raw_content
Found suspicious code: eval(
Detected 1 base64 encoded strings
Found flag: rtl{f0rm_b64_flag} in base64_decoded
Analysis complete. Report saved to analysis_report.json

Summary:
- Flags found: 2
- URLs found: 0
- Risky objects: 6
- Malicious code patterns: 1
- Evasion techniques: 2

Flags: rtl{r1chmedia_backd00r}, rtl{f0rm_b64_flag}
```

### JSON Report Structure

```json
{
  "flags": [
    "rtl{example_flag1}",
    "rtl{example_flag2}"
  ],
  "urls": [
    "https://malicious.com/exfil",
    "https://attacker.com/collect"
  ],
  "risky_objects": [
    {
      "type": "/JavaScript",
      "location": "raw_content"
    },
    {
      "type": "/EmbeddedFiles",
      "location": "page_1_objects"
    }
  ],
  "malicious_code": [
    "Suspicious JavaScript: app.launchURL( in page_1_text",
    "Suspicious JavaScript: eval( in base64_decoded"
  ],
  "evasion_techniques": [
    "Base64 encoding detected (5 instances)",
    "Hex encoding detected (2 instances)",
    "PDF is encrypted"
  ]
}
```

## Detected Elements

### Flags
- Format: `rtl{...}` (case-insensitive)
- Searched in: metadata, page text, comments, annotations, streams, decoded content

### URLs
- Formats: `https://website` or `https://website/endpoint`
- Commonly found in: `/URI` actions, JavaScript code, form submissions

### Risky PDF Objects
- `/JavaScript` or `/JS`: Embedded JavaScript code
- `/EmbeddedFiles`: Attached files that could contain malware
- `/OpenAction` or `/AA`: Automatic actions when PDF is opened
- `/Launch`: Executes external programs
- `/URI`: Links to external URLs
- `/SubmitForm`: Form data submission
- `/ImportData`: Data import functionality
- `/GoToR`: Remote document references

### Malicious Code Patterns
- `app.launchURL()`: Opens URLs automatically
- `this.submitForm()`: Submits form data
- `eval()`: Dynamic code execution
- `unescape()`: Decodes obfuscated content

### Evasion Techniques
- **Base64 Encoding**: Obfuscated content using base64
- **Hex Encoding**: Content encoded in hexadecimal
- **Encryption**: Password-protected or encrypted PDFs
- **Stream Compression**: Compressed object streams

## Error Handling

The tool gracefully handles:
- **Malformed PDFs**: Continues analysis with available parsers
- **Encrypted Files**: Attempts decryption with empty password
- **Missing Dependencies**: Provides clear installation instructions
- **File Not Found**: Clear error message with file path
- **Permission Errors**: Handles read permission issues

## Security Considerations

- **Read-Only Analysis**: Tool never executes or modifies the PDF
- **No Dynamic Evaluation**: Avoids `eval()` or `exec()` functions
- **Safe Decoding**: Uses error-tolerant decoding methods
- **Sandboxed Environment**: Recommended to run in isolated environment

## Troubleshooting

### Common Issues

1. **ImportError: No module named 'PyPDF2'**
   ```bash
   pip install PyPDF2 pdfminer.six
   ```

2. **Permission Denied**
   ```bash
   chmod +x pdf_analyzer.py
   ```

3. **Encrypted PDF Analysis**
   - Tool attempts automatic decryption with empty password
   - For password-protected PDFs, manual decryption may be required

4. **Large PDF Files**
   - Analysis may take longer for large files
   - Use `--verbose` to monitor progress

### Performance Tips

- For large PDFs, consider analyzing specific pages if possible
- Use SSD storage for faster file I/O
- Ensure sufficient RAM for large PDF processing

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

This tool is provided for educational and forensic analysis purposes. Use responsibly and in accordance with applicable laws and regulations.

## Changelog

### Version 1.0
- Initial release with basic PDF analysis
- Support for PyPDF2 and pdfminer.six
- Flag and URL detection
- JSON report generation
- Verbose logging support
