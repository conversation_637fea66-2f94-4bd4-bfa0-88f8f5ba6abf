#!/usr/bin/env python3
"""
Simple flag extraction script
Runs the PDF analyzer and extracts only the flags
"""

import json
import subprocess
import sys

def extract_flags(pdf_file):
    """Extract flags from PDF using the analyzer"""
    print(f"🔍 Extracting flags from: {pdf_file}")
    print("=" * 50)
    
    # Run the analyzer
    cmd = f"bash -c 'source venv/bin/activate && python pdf_analyzer.py {pdf_file}'"
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            # Load the JSON report
            with open("analysis_report.json", 'r') as f:
                report = json.load(f)
            
            flags = report.get('flags', [])
            
            if flags:
                print(f"🚩 FLAGS FOUND: {len(flags)}")
                print("-" * 30)
                for i, flag in enumerate(flags, 1):
                    print(f"{i}. {flag}")
                
                print("\n📋 FLAGS (copy-paste format):")
                print("-" * 30)
                for flag in flags:
                    print(flag)
                    
                return flags
            else:
                print("❌ No flags found in the PDF")
                return []
                
        else:
            print("❌ Analysis failed")
            print("Error:", result.stderr)
            return []
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return []

def main():
    if len(sys.argv) != 2:
        print("Usage: python extract_flags.py <pdf_file>")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    flags = extract_flags(pdf_file)
    
    print(f"\n🎯 SUMMARY: Found {len(flags)} flag(s)")

if __name__ == "__main__":
    main()
